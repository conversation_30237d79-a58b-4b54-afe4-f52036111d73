# 桩类型搜索功能修复说明

## 问题描述

1. **搜索区域高度过高**：搜索区域占用过多垂直空间
2. **重置功能异常**：点击重置后，桩类型选择器的值仍然显示，没有正确清空
3. **搜索图标改为文字**：将搜索图标改为"选择"文字，提升用户体验

## 修复内容

### 1. 高度优化

**修改文件：** `stake-menu.component.scss`

- 减少搜索容器内边距：`12px 16px` → `8px 16px`
- 缩小搜索行间距：`margin-bottom: 10px` → `8px`
- 调整所有组件高度：从 36-38px 减少到 32px
- 优化字体大小：从 14px 调整为 13px
- 调整高度计算：搜索区域高度从 120px 优化为 90px

### 2. 重置功能修复

**问题根源：** InputSearchComponent 的 `writeValue` 方法只处理 `null` 值，没有处理空字符串

**修改文件：** `input-search.component.ts`

```typescript
// 修复前
writeValue(obj: any): void {
  if (obj === null) {
    this.name = obj;
  }
}

// 修复后
writeValue(obj: any): void {
  if (obj === null || obj === undefined || obj === '') {
    this.name = null;
  }
}
```

**修改文件：** `option-source.component.ts`

```typescript
// 优化 getRadioName 方法
getRadioName(value: any) {
  if (!value || value === null || value === '') {
    return null;
  }
  if (this.list.length > 0) {
    return this.list.find((item: any) => item.value === value)?.name;
  }
  return null;
}

// 优化 initData 方法
initData(data: any) {
  if (this.labelName && this.labelValue) {
    this.transformation(data, this.labelValue, this.labelName);
  } else {
    this.list = data;
  }
  // 只有当radioValue有有效值时才回显
  if (this.radioValue && this.radioValue !== null && this.radioValue !== '') {
    this.onChangeRadio(this.radioValue);
  }
}
```

**修改文件：** `stake-menu.component.ts`

```typescript
// 优化重置方法
onReset(): void {
  this.params = '';
  this.stakeType = '';
  this.isSearching = false;

  // 使用 null 值重置选择器
  if (this.stakeTypeSelector) {
    this.stakeTypeSelector.writeValue(null);
    this.stakeTypeSelector.value = null;
  }

  // 强制触发变更检测
  setTimeout(() => {
    this.cd.detectChanges();
  }, 100);

  // 重新加载数据
  if (this.isPage) {
    this.searchData = new StakeParams();
    this.loadMenuTree(this.pipelineId);
  } else {
    this.items = this.stakeTree;
  }
}

// 优化桩类型变化处理
onStakeTypeChange(value: string): void {
  this.stakeType = value;
  // 只有当值不为空且不为null时才自动触发搜索
  if (value && value !== null && value !== '') {
    this.onSearch();
  }
}
```

### 3. 桩菜单搜索按钮改为文字

**修改文件：** `stake-menu.component.html`

```html
<!-- 修改前 -->
<ion-icon name="search-outline" class="search-trigger-icon" (click)="onSearch()"></ion-icon>

<!-- 修改后 -->
<span class="search-trigger-text" (click)="onSearch()">搜索</span>
```

**修改文件：** `stake-menu.component.scss`

```scss
// 搜索触发文字按钮
.search-trigger-text {
  font-size: 13px;
  font-weight: 500;
  color: #007bff;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #e7f3ff;
  border: 1px solid #007bff;
  transition: all 0.2s ease;
  min-width: 50px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  white-space: nowrap;

  &:hover {
    background-color: #007bff;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
  }
}
```

## 修复效果

### ✅ 高度优化
- 搜索区域更加紧凑，节省屏幕空间
- 所有组件高度统一为 32px，视觉更协调
- 移动端体验更佳

### ✅ 重置功能
- 点击重置后，桩类型选择器正确清空显示
- 避免重置时触发不必要的搜索请求
- 数据状态完全重置

### ✅ UI改进
- 桩菜单搜索图标改为"搜索"文字，更直观
- 搜索文字按钮有悬停和点击效果
- 保持与整体设计风格一致
- 桩类型选择器保持原有的图标显示

## 测试建议

1. **重置功能测试**：
   - 选择桩类型后点击重置，确认选择器显示为空
   - 输入关键词后点击重置，确认输入框清空
   - 重置后确认数据重新加载

2. **搜索功能测试**：
   - 选择桩类型后自动触发搜索
   - 桩类型 + 关键词组合搜索
   - 清空桩类型选择后不触发搜索

3. **UI体验测试**：
   - 确认搜索区域高度合适
   - 确认"选择"文字按钮交互正常
   - 确认移动端触摸体验良好
