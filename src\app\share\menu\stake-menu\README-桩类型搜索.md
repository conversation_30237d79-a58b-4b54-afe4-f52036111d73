# 桩类型搜索功能实现

## 功能概述

在桩菜单组件中添加了桩类型搜索条件，支持用户按桩类型筛选桩数据。桩类型包括：里程桩、阴保桩、转角桩、警示牌、标志桩。

## 设计方案

### UI组件选择：下拉选择（QuickOptionSelectComponent）

**选择理由：**
1. **选项数量适中**：5个桩类型选项超过QuickOptionSelectComponent的自动切换阈值（3个），自动使用下拉模式
2. **移动端友好**：下拉选择在移动端有更好的交互体验，节省屏幕空间
3. **UI一致性**：与项目中其他选择组件保持一致的设计风格
4. **数据动态加载**：通过接口获取桩类型数据，便于后期维护和扩展

### 数据来源

- **接口地址**：`/work-basic/api/v2/basic/dict/msg/list?dictCode=fcMarkerType`
- **数据格式**：使用字典数据，`dictValue` 字段作为显示名称和值
- **动态加载**：组件初始化时自动从接口获取最新的桩类型数据

## 实现细节

### HTML结构

```html
<!-- 桩类型选择 -->
<div class="search-row">
  <div class="search-item stake-type-item">
    <span class="search-label">桩类型：</span>
    <ost-quick-option-select
      #stakeTypeSelector
      class="stake-type-selector"
      [isShowIcon]="false"
      [(ngModel)]="stakeType"
      (ngModelChange)="onStakeTypeChange($event)"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=fcMarkerType"
      placeholder="请选择桩类型"
      [disabled]="loading">
    </ost-quick-option-select>
  </div>
  <ion-icon name="search-outline" class="search-trigger-icon" (click)="onSearch()"></ion-icon>
</div>
```

### TypeScript逻辑

```typescript
/** 桩类型搜索参数 */
stakeType = '';

/**
 * 桩类型变化处理
 */
onStakeTypeChange(value: string): void {
  this.stakeType = value;
  // 桩类型变化时自动触发搜索
  this.onSearch();
}

/**
 * 执行后端搜索（分页模式）
 */
private performBackendSearch(): void {
  // 构建搜索参数
  this.searchData = new StakeParams();
  this.searchData.pipelineId = this.pipelineId;

  // 添加关键词搜索参数
  if (this.params.trim()) {
    (this.searchData as any)[this.searchFieldName] = this.params.trim();
  }

  // 添加桩类型搜索参数
  if (this.stakeType) {
    (this.searchData as any).stakeType = this.stakeType;
  }

  this.executeRequest(this.searchData, (res) => {
    this.items = res;
  });
}

/**
 * 执行前端搜索（非分页模式）
 */
private performFrontendSearch(): void {
  this.items = this.filterItems(this.stakeTree, this.params, this.stakeType);
}

/**
 * 过滤搜索数据
 */
private filterItems(items: OstTreeListItem[], query: string, stakeType: string): OstTreeListItem[] {
  if (!items?.length) return items;

  return items.filter(item => {
    // 关键词过滤
    const matchesQuery = !query || item.title?.toLowerCase().includes(query.toLowerCase());

    // 桩类型过滤
    const matchesStakeType = !stakeType ||
      item.data?.stakeType === stakeType ||
      item.data?.type === stakeType ||
      item.data?.markerType === stakeType;

    return matchesQuery && matchesStakeType;
  });
}

/**
 * 重置搜索
 */
onReset(): void {
  this.params = '';
  this.stakeType = '';
  this.isSearching = false;

  // 强制重置桩类型选择器的值
  if (this.stakeTypeSelector) {
    this.stakeTypeSelector.writeValue('');
    this.stakeTypeSelector.value = '';
  }

  // 重新加载数据
  if (this.isPage) {
    this.searchData = new StakeParams();
    this.loadMenuTree(this.pipelineId);
  } else {
    this.items = this.stakeTree;
  }
}
```

## 样式设计

### 移动端优化样式

- **紧凑布局**：桩类型选择器与搜索按钮在同一行，节省垂直空间
- **响应式设计**：选择器最大宽度180px，适配不同屏幕尺寸
- **交互反馈**：悬停和点击状态有明显的视觉反馈
- **一致性**：与关键词搜索框保持相同的视觉风格

### 关键样式特点

```scss
&.stake-type-item {
  .search-label {
    min-width: 70px;
    color: #495057;
    font-weight: 500;
  }

  ost-quick-option-select {
    flex: 1;
    max-width: 180px;
    
    // 下拉模式样式优化
    ::ng-deep .input-search {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      min-height: 38px;
      
      &:focus-within {
        border-color: #007bff;
        background-color: #ffffff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }
    }
  }
}
```

## 功能特点

1. **自动搜索**：桩类型选择变化时自动触发搜索，提升用户体验
2. **双模式支持**：同时支持分页和非分页模式的搜索
3. **组合搜索**：支持桩类型和关键词的组合搜索
4. **重置功能**：一键重置所有搜索条件
5. **加载状态**：搜索过程中禁用选择器，防止重复操作
6. **数据同步**：与后端接口数据保持同步，支持动态更新

## 使用说明

1. **选择桩类型**：点击桩类型下拉框，选择需要筛选的桩类型
2. **组合搜索**：可以同时使用桩类型和关键词进行组合搜索
3. **重置搜索**：点击"重置"按钮清空所有搜索条件
4. **实时搜索**：桩类型选择后自动执行搜索，无需手动点击搜索按钮

## 扩展性

- **新增桩类型**：通过后端字典配置即可添加新的桩类型，无需修改前端代码
- **搜索字段**：可以根据后端数据结构调整搜索字段映射
- **UI定制**：可以通过修改样式文件调整选择器的外观和交互效果
