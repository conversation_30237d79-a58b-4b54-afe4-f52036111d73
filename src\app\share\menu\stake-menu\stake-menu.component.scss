// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 紧凑搜索容器样式
.compact-search-container {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  z-index: 100;
  flex-shrink: 0;
  padding: 8px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // 搜索行样式
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    // 搜索项样式
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 10px;

      .search-label {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        margin-right: 8px;
        min-width: 60px;
        flex-shrink: 0;
      }

      // 桩类型选择项特殊样式
      &.stake-type-item {
        .search-label {
          min-width: 60px;
          color: #495057;
          font-weight: 400;
          font-size: 13px;
        }

      }

      // 关键词输入框特殊样式
      &.search-input-item {
        .compact-search-input {
          flex: 1;
          --padding-start: 10px;
          --padding-end: 10px;
          --color: #495057;
          --placeholder-color: #999999;
          font-size: 13px;
          height: 32px;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          background-color: #f8f9fa;
          transition: all 0.2s ease;

          &:focus-within {
            border-color: #007bff;
            background-color: #ffffff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
          }
        }
      }
    }

    // 搜索触发文字按钮
    .search-trigger-text {
      font-size: 13px;
      font-weight: 500;
      color: #007bff;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 4px;
      background-color: #e7f3ff;
      border: 1px solid #007bff;
      transition: all 0.2s ease;
      min-width: 50px;
      min-height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
      white-space: nowrap;

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
      }
    }

    // 重置链接
    .reset-link {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      cursor: pointer;
      padding: 6px 10px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;
      min-height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

ost-quick-option-select{
    --margin-inline-start: 0px !important;
    flex: 1; /* 占用剩余所有空间 */
    min-width: 0; /* 防止flex子项溢出 */
    width: 100%;
}