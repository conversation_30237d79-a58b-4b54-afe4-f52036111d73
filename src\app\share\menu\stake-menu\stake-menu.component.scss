// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 紧凑搜索容器样式
.compact-search-container {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  z-index: 100;
  flex-shrink: 0;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // 搜索行样式
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    // 搜索项样式
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 12px;

      .search-label {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        margin-right: 10px;
        min-width: 60px;
        flex-shrink: 0;
      }

      // 桩类型选择项特殊样式
      &.stake-type-item {
        .search-label {
          min-width: 70px;
          color: #495057;
          font-weight: 500;
        }

        ost-quick-option-select {
          flex: 1;
          max-width: 180px;

          // 优化选择器内部样式
          ::ng-deep {
            .quick-radio-group {
              gap: 6px;
              flex-wrap: wrap;

              ion-item-divider {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 6px 12px;
                transition: all 0.2s ease;
                min-height: 34px;
                cursor: pointer;

                &:hover {
                  border-color: #007bff;
                  background-color: #e7f3ff;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
                }

                &:active {
                  transform: translateY(0);
                }

                ion-label {
                  font-size: 13px;
                  color: #495057;
                  margin: 0;
                  font-weight: 400;
                }

                ion-radio {
                  width: 14px;
                  height: 14px;
                  margin-right: 6px;
                }

                // 选中状态样式
                &.radio-checked {
                  background-color: #007bff;
                  border-color: #007bff;
                  color: #ffffff;

                  ion-label {
                    color: #ffffff;
                    font-weight: 500;
                  }
                }
              }
            }

            // 列表选择模式样式优化
            .input-search {
              background-color: #f8f9fa;
              border: 1px solid #e9ecef;
              border-radius: 6px;
              padding: 8px 12px;
              transition: all 0.2s ease;
              min-height: 38px;

              &:focus-within {
                border-color: #007bff;
                background-color: #ffffff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
              }

              ion-input {
                font-size: 14px;
                --color: #495057;
                --placeholder-color: #999999;
                --padding-start: 0;
                --padding-end: 0;
              }
            }
          }
        }
      }

      // 关键词输入框特殊样式
      &.search-input-item {
        .compact-search-input {
          flex: 1;
          --padding-start: 12px;
          --padding-end: 12px;
          --color: #495057;
          --placeholder-color: #999999;
          font-size: 14px;
          height: 36px;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          background-color: #f8f9fa;
          transition: all 0.2s ease;

          &:focus-within {
            border-color: #007bff;
            background-color: #ffffff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
          }
        }
      }
    }

    // 搜索触发图标
    .search-trigger-icon {
      font-size: 20px;
      color: #007bff;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      background-color: #e7f3ff;
      border: 1px solid #007bff;
      transition: all 0.2s ease;
      min-width: 36px;
      min-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #007bff;
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    // 重置链接
    .reset-link {
      font-size: 13px;
      font-weight: 400;
      color: #666666;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;
      min-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #495057;
        background-color: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}