# MapComponent 使用说明

## 概述

`MapComponent` 是一个基于 OpenLayers 的 Angular 地图组件，提供了完整的地图显示、交互和业务功能。该组件支持多种图层管理、定位服务、关键点渲染等功能。

## 组件信息

- **选择器**: `ost-map`
- **模板**: `./map.component.html`
- **样式**: `./map.component.scss`
- **依赖**: OpenLayers, Ionic Angular

## 输入属性 (Input Properties)

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `taskCode` | `string` | - | 任务代码，用于图层参数传递 |
| `center` | `Coordinate` | `[112.5504237, 37.8736249]` | 地图中心坐标 |
| `zoom` | `number` | `14` | 初始缩放级别 |
| `projection` | `string` | `'EPSG:4326'` | 空间参考系 |
| `addMapClickEvent` | `boolean` | `false` | 是否添加地图点击事件 |
| `layerIds` | `string[]` | - | 需要操作的业务图层ID列表 |
| `showLocationInfo` | `boolean` | `false` | 是否显示位置信息小工具 |
| `showLocationProviderButton` | `boolean` | `false` | 是否显示定位模式选择按钮 |
| `inspectionMethod` | `string` | - | 巡检方式，可选值：巡视 巡查 | 不传默认显示全部

## 输出事件 (Output Events)

| 事件名 | 类型 | 描述 |
|--------|------|------|
| `mapLoaded` | `EventEmitter<void>` | 地图加载完成时触发 |

## 公共方法 (Public Methods)

### setCurrentLocation(coordinate, accuracy?, followView?)

设置当前位置并更新地图显示。

**参数:**
- `coordinate: Coordinate` - 位置坐标
- `accuracy?: number` - 定位精度（米），默认为0
- `followView?: boolean` - 是否跟随视图，默认为true

**示例:**
```typescript
this.mapComponent.setCurrentLocation([116.404, 39.915], 10, true);
```

### setKeyPoints(points)

批量渲染关键点及其范围。

**参数:**
- `points: any[]` - 关键点数组，包含坐标和状态信息

**示例:**
```typescript
const keyPoints = [
  { id: '1', coordinate: [116.404, 39.915], status: '已巡' },
  { id: '2', coordinate: [116.405, 39.916], status: '未巡' }
];
this.mapComponent.setKeyPoints(keyPoints);
```

### updateKeyPointStatus(id, status)

更新单个关键点的状态。

**参数:**
- `id: string` - 关键点ID
- `status: '已巡' | '未巡'` - 新状态

**示例:**
```typescript
this.mapComponent.updateKeyPointStatus('1', '已巡');
```

### triggerLocation()

触发定位功能。

**示例:**
```typescript
this.mapComponent.triggerLocation();
```

### refreshBusinessLayer(layerId, coordinate?)

刷新指定的业务图层。

**参数:**
- `layerId: string` - 图层ID
- `coordinate?: Coordinate` - 可选的中心坐标

**示例:**
```typescript
this.mapComponent.refreshBusinessLayer('inspect_point', [116.404, 39.915]);
```

## 使用示例

### 基本用法

```html
<ost-map
  [center]="[116.404, 39.915]"
  [zoom]="15"
  [addMapClickEvent]="true"
  [showLocationInfo]="true"
  [layerIds]="['inspect_point', 'inspect_event']"
  (mapLoaded)="onMapLoaded()">
</ost-map>
```

### 在组件中使用

```typescript
import { Component, ViewChild } from '@angular/core';
import { MapComponent } from './path/to/map.component';

@Component({
  selector: 'app-example',
  template: `
    <ost-map
      #mapRef
      [center]="mapCenter"
      [zoom]="mapZoom"
      [addMapClickEvent]="true"
      [showLocationInfo]="true"
      (mapLoaded)="onMapReady()">
    </ost-map>
    <button (click)="updateLocation()">更新位置</button>
  `
})
export class ExampleComponent {
  @ViewChild('mapRef') mapComponent: MapComponent;
  
  mapCenter = [116.404, 39.915];
  mapZoom = 14;
  
  onMapReady() {
    console.log('地图加载完成');
    // 设置初始关键点
    this.mapComponent.setKeyPoints([
      { id: '1', coordinate: [116.404, 39.915], status: '未巡' }
    ]);
  }
  
  updateLocation() {
    // 更新当前位置
    this.mapComponent.setCurrentLocation([116.405, 39.916], 15);
  }
}
```

### 高级用法 - 监听地图事件

```typescript
export class AdvancedMapComponent {
  @ViewChild('mapRef') mapComponent: MapComponent;
  
  ngAfterViewInit() {
    // 等待地图加载完成后进行操作
    this.mapComponent.mapLoaded.subscribe(() => {
      this.setupMapInteractions();
    });
  }
  
  private setupMapInteractions() {
    // 设置关键点
    const keyPoints = this.loadKeyPointsFromService();
    this.mapComponent.setKeyPoints(keyPoints);
    
    // 设置当前位置
    this.getCurrentPosition().then(position => {
      this.mapComponent.setCurrentLocation(
        [position.longitude, position.latitude],
        position.accuracy
      );
    });
  }
}
```

## 配置选项

### 图层配置

组件支持多种图层类型：
- **基础图层**: 底图服务（瓦片、WMS等）
- **业务图层**: 业务数据图层
- **定位图层**: 当前位置显示
- **关键点图层**: 关键点标记

### 坐标系统

默认使用 `EPSG:4326` 坐标系，支持其他坐标系配置。

## 注意事项

1. **性能优化**: 大量关键点时建议分批加载
2. **内存管理**: 组件会自动清理资源，无需手动销毁
3. **事件处理**: 地图点击事件使用了防抖处理，避免频繁触发
4. **错误处理**: 组件内置了坐标验证和错误恢复机制

## MapService 服务

`MapService` 是地图组件的配套服务，提供了便捷的地图操作方法和标记管理功能。

### 服务注入

```typescript
import { MapService } from './path/to/map.service';

@Component({
  // ...
})
export class YourComponent {
  constructor(private mapService: MapService) {}
}
```

### 服务方法

#### registerMapComponent(mapComponent)

注册地图组件实例到服务中。

**参数:**
- `mapComponent: MapComponent` - 地图组件实例

**说明:** 通常在地图组件的 `ngOnInit` 中自动调用，无需手动调用。

#### getMapComponent()

获取当前注册的地图组件实例。

**返回值:** `MapComponent | null`

**示例:**
```typescript
const mapComponent = this.mapService.getMapComponent();
if (mapComponent) {
  // 使用地图组件
}
```

#### addImageMarker(coordinate, imageSrc, scale?)

在指定坐标添加图片标记。

**参数:**
- `coordinate: Coordinate` - 标记坐标
- `imageSrc: string` - 图片路径
- `scale?: number` - 缩放比例，默认为1

**返回值:** `Feature<Point> | null`

**示例:**
```typescript
// 添加自定义图标标记
const marker = this.mapService.addImageMarker(
  [116.404, 39.915],
  'assets/icons/custom-marker.png',
  1.5
);
```

#### clearMarkers()

清除所有标记。

**示例:**
```typescript
this.mapService.clearMarkers();
```

#### setMapCenter(coordinate, accuracy?, followView?)

设置地图中心位置。

**参数:**
- `coordinate: Coordinate` - 中心坐标
- `accuracy?: number` - 精度，默认为0
- `followView?: boolean` - 是否跟随视图，默认为true

**示例:**
```typescript
this.mapService.setMapCenter([116.404, 39.915], 10, true);
```

#### moveMapToCoordinate(coordinate, duration?, targetZoom?)

移动地图到指定坐标。

**参数:**
- `coordinate: Coordinate` - 目标坐标
- `duration?: number` - 动画持续时间（毫秒），默认为500
- `targetZoom?: number` - 目标缩放级别，可选

**示例:**
```typescript
// 平滑移动到指定位置
this.mapService.moveMapToCoordinate([116.404, 39.915], 1000, 16);
```

### 服务使用示例

```typescript
import { Component, OnInit } from '@angular/core';
import { MapService } from './path/to/map.service';

@Component({
  selector: 'app-map-example',
  template: `
    <ost-map
      [center]="mapCenter"
      [zoom]="mapZoom"
      (mapLoaded)="onMapLoaded()">
    </ost-map>
    <ion-button (click)="addMarker()">添加标记</ion-button>
    <ion-button (click)="moveToLocation()">移动到位置</ion-button>
    <ion-button (click)="clearAllMarkers()">清除标记</ion-button>
  `
})
export class MapExampleComponent implements OnInit {
  mapCenter = [116.404, 39.915];
  mapZoom = 14;

  constructor(private mapService: MapService) {}

  ngOnInit() {}

  onMapLoaded() {
    console.log('地图加载完成');
  }

  addMarker() {
    // 添加自定义标记
    this.mapService.addImageMarker(
      [116.405, 39.916],
      'assets/icons/location-pin.png',
      1.2
    );
  }

  moveToLocation() {
    // 移动到新位置
    this.mapService.moveMapToCoordinate(
      [116.406, 39.917],
      800,
      15
    );
  }

  clearAllMarkers() {
    // 清除所有标记
    this.mapService.clearMarkers();
  }
}
```

## 依赖项

确保项目中已安装以下依赖：

```json
{
  "ol": "^6.9.0",
  "@ionic/angular": "^5.5.2",
  "rxjs": "~6.6.0"
}
```

## 故障排除

### 常见问题

1. **地图不显示**: 检查容器元素是否有有效的宽高
2. **定位失败**: 确保应用有定位权限
3. **图层加载失败**: 检查网络连接和图层服务URL
4. **关键点不显示**: 验证坐标格式和数据结构

### 调试技巧

```typescript
// 启用控制台日志
console.log('地图实例:', this.mapComponent.map);
console.log('当前视图:', this.mapComponent.view);
console.log('图层列表:', this.mapComponent.businessLayerList.getArray());
```

## 更新日志

- **v1.0.0**: 初始版本，支持基础地图功能
- **v1.1.0**: 添加关键点渲染功能
- **v1.2.0**: 优化性能，添加错误处理
- **v1.3.0**: 支持动态图层刷新

## 许可证

本组件遵循项目整体许可证。