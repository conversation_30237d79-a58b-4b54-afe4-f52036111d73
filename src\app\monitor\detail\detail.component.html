<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start" class="title-start-back">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <!-- 实时监控 -->
    <ion-title>实时监控</ion-title>
  </ion-toolbar>
</ion-header>
<div class="monitor-top">
  <ost-map [zoom]="7" [layerIds]="['inspect_point', 'p_pipe_joint_info']" #ostMap></ost-map>
</div>
<div class="monitor-center">
  <!-- 巡线人员 -->
  <div class="title">巡线人员</div>
  <!-- 提示信息 -->
  <div class="tips">
    <!-- 在线人数 -->
    <span class="on-line">在线人数：{{ taskOnline.onlineCount }}</span>
    <!-- 离线人数 -->
    <span class="off-line">离线人数：{{ taskOnline.unOnlineCount }}</span>
  </div>
</div>
<ion-content [fullscreen]="false">
  <div class="monitor-bottom">
    <div>
      <!-- 下拉刷新 -->
      <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
        <ion-refresher-content></ion-refresher-content>
      </ion-refresher>
      <!-- 列表 -->
      <ng-container *ngIf="taskOnlineUserList.length > 0; else noData">
        <ion-grid>
          <ion-row>
            <ng-container *ngFor="let item of taskOnlineUserList">
              <ion-col size="6">
                <div class="user" [class]="{ select: item.select }">
                  <div class="user-name">人员：{{ item.userName }}</div>
                  <div
                    class="on-location"
                    [class]="{
                      'location-success': item.select,
                      'location-primary': !item.select
                    }"
                    (click)="onLocation(item)"
                  >
                    <ion-icon name="location-outline"></ion-icon>
                    定位
                  </div>
                </div>
              </ion-col>
            </ng-container>
          </ion-row>
        </ion-grid>
      </ng-container>
      <!-- 无数据占位图 -->
      <ng-template #noData>
        <div class="no-data">
          <img src="assets/menu/box2.png" style="padding-top: 50px" />
          <!-- 暂无数据 -->
          <span class="no-data-span">暂无数据</span>
        </div>
      </ng-template>
    </div>
  </div>
</ion-content>
