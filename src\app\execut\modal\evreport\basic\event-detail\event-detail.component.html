<!-- 事件详情 -->
<div *ngIf="eventType === '管道占压'">
  <ost-form-item>
    <ion-label>桩号：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      placeholder="点击选择桩号"
      [name]="initialValues.stakeName"
      formControlName="stakeCode" slot="end"
    >
      <search-source-stake 
        [pipelineId]="pipelineId"
        [isPage]="true"
        searchFieldName="stakeName"
        interfaceUrl="/work-basic/api/v2/basic/stake/msg/grid"
      >
      </search-source-stake>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item [divLine]="true" [required]="false">
    <ion-label>桩偏移量：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" type="number" formControlName="stakeOffset"></ion-input>
  </ost-form-item>

  <ost-form-item>
    <ion-label>占压物：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="pressorSubstance"></ion-input>
  </ost-form-item>

  <ost-form-item>
    <ion-label>占压方式：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="pressorType"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>管道中心距离(米)：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" type="number" formControlName="pipeCentreDistance"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>占压物归属单位：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="pressorUnit"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false" [divLine]="true">
    <ion-label>占压物所属县、乡、村：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="pressorSubstancePlace"></ion-input>
  </ost-form-item>
</div>

<!-- 设备失效 -->
<div *ngIf="eventType === '设备失效'">
  <ost-form-item [divLine]="true">
    <ion-label>站场：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      [name]="initialValues.stationName"
      placeholder="点击选择站场"
      formControlName="stationCode" slot="end"
    >
      <search-source-stake 
        [pipelineId]="pipelineId"
        labelName="stationName"
        labelValue="stationCode"
        interfaceUrl="/work-basic/api/v2/basic/station/msg/list"
      ></search-source-stake>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item>
    <ion-label>设备编号：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="deviceName"></ion-input>
  </ost-form-item>

  <ost-form-item>
    <ion-label>设备类型：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      placeholder="点击选择设备类型"
      formControlName="deviceTypeCode" slot="end"
    >
      <ost-search-source-tree
        [labelName]="'deviceTypeName'"
        [labelValue]="'deviceTypeCode'"
        interfaceUrl="/work-device/api/v2/device/type/msg/tree">
      </ost-search-source-tree>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item>
    <ion-label>失效日期：</ion-label>
    <ion-datetime 
      [readonly]="modelMode === DetailsMode.SEE" 
      displayFormat="YYYY-MM-DD" 
      formControlName="expireDate"
      placeholder="选择日期"
      cancelText="取消"
      doneText="确定">
    </ion-datetime>
  </ost-form-item>

  <ost-form-item>
    <ion-label>紧急级别：</ion-label>
    <ost-quick-option-select
      formControlName="emergncyLevel"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=emergncyLevel"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [divLine]="true">
    <ion-label>设备状态：</ion-label>
    <ost-quick-option-select
      formControlName="currentStatus"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=current_status"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>失效描述：</ion-label>
    <ion-textarea 
      [readonly]="modelMode === DetailsMode.SEE" 
      rows="3" 
      formControlName="expireDesc"
      placeholder="请输入失效描述">
    </ion-textarea>
  </ost-form-item>

  <ost-form-item [required]="false" [divLine]="true">
    <ion-label>失效原因：</ion-label>
    <ion-textarea 
      [readonly]="modelMode === DetailsMode.SEE" 
      rows="3" 
      formControlName="expireReason"
      placeholder="请输入失效原因">
    </ion-textarea>
  </ost-form-item>
</div>

<!-- 第三方施工 -->
<div *ngIf="eventType === '第三方施工'">
  <ost-form-item>
    <ion-label>起始桩：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      [name]="initialValues.beginStakeName"
      placeholder="点击选择桩号"
      formControlName="beginStakeCode" slot="end"
    >
      <search-source-stake         
        [pipelineId]="pipelineId"
        [isPage]="true"
        searchFieldName="stakeName"
        interfaceUrl="/work-basic/api/v2/basic/stake/msg/grid">
      </search-source-stake>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item [divLine]="true">
    <ion-label>终止桩：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      [name]="initialValues.endStakeName"
      placeholder="点击选择桩号"
      formControlName="endStakeCode" slot="end"
    >
      <search-source-stake         
        [pipelineId]="pipelineId"
        [isPage]="true"
        searchFieldName="stakeName"
        interfaceUrl="/work-basic/api/v2/basic/stake/msg/grid"></search-source-stake>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>起始偏移量：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" type="number" formControlName="beginOffset"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>终止偏移量：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" type="number" formControlName="endOffset"></ion-input>
  </ost-form-item>

  <ost-form-item>
    <ion-label>施工类型：</ion-label>
    <ost-quick-option-select
      formControlName="construcType"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=construcType"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [divLine]="true">
    <ion-label>隐患等级：</ion-label>
    <ost-quick-option-select
      formControlName="threatLevel"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=threatlevel"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>施工项目名称：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="projectName"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>施工单位：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="constructionUnit"></ion-input>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>施工联系人：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="dutyPerson"></ion-input>
  </ost-form-item>

  <ost-form-item [divLine]="true" [required]="false">
    <ion-label>联系方式：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" formControlName="dutypersonPhone"></ion-input>
  </ost-form-item>
</div>

<!-- 隐患上报 -->
<div *ngIf="eventType === '隐患上报'">
  <ost-form-item>
    <ion-label>桩号：</ion-label>
    <ost-input-search
      style="margin: 0px;"
      [readonly]="modelMode === DetailsMode.SEE"
      placeholder="点击选择桩号"
      [name]="initialValues.stakeName"
      formControlName="stakeCode" slot="end"
    >
      <search-source-stake         
        [pipelineId]="pipelineId"
        [isPage]="true"
        searchFieldName="stakeName"
        interfaceUrl="/work-basic/api/v2/basic/stake/msg/grid">
      </search-source-stake>
    </ost-input-search>
  </ost-form-item>

  <ost-form-item [divLine]="true" [required]="false">
    <ion-label>偏移量：</ion-label>
    <ion-input [readonly]="modelMode === DetailsMode.SEE" type="number" formControlName="stakeOffset"></ion-input>
  </ost-form-item>

  <ost-form-item>
    <ion-label>隐患类型：</ion-label>
    <ost-quick-option-select
      formControlName="hiddenDangerType"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=threatlevel"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item>
    <ion-label>隐患严重程度：</ion-label>
    <ost-quick-option-select
      formControlName="severityLevel"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=severityLevel"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>影响范围：</ion-label>
    <ion-textarea 
      [readonly]="modelMode === DetailsMode.SEE" 
      rows="3" 
      formControlName="scopeOfInfluence"
      placeholder="请输入影响范围">
    </ion-textarea>
  </ost-form-item>

  <ost-form-item [required]="false" [divLine]="true">
    <ion-label>隐患描述：</ion-label>
    <ion-textarea 
      [readonly]="modelMode === DetailsMode.SEE" 
      rows="3" 
      formControlName="hiddenDangerDesc"
      placeholder="请输入隐患描述">
    </ion-textarea>
  </ost-form-item>

  <ost-form-item>
    <ion-label>处理状态：</ion-label>
    <ost-quick-option-select
      formControlName="processStatus"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      [modalType]="modelMode"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=processStatus"
    ></ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [required]="false">
    <ion-label>处理人：</ion-label>
    <ost-quick-option-select
      formControlName="processPeopleCode"
      [modalType]="modelMode"
      [labelName]="'userName'"
      [labelValue]="'userCode'"
      interfaceUrl="/work-basic/api/v2/basic/user/msg/list">
    </ost-quick-option-select>
  </ost-form-item>

  <ost-form-item [required]="false" [divLine]="true">
    <ion-label>处理措施：</ion-label>
    <ion-textarea 
      [readonly]="modelMode === DetailsMode.SEE" 
      rows="3" 
      formControlName="handlingMeasures"
      placeholder="请输入处理措施">
    </ion-textarea>
  </ost-form-item>
</div>