import { Component, Input, OnInit } from '@angular/core';
import { FileOpener } from '@ionic-native/file-opener/ngx';
import { FileTransfer, FileTransferObject } from '@ionic-native/file-transfer/ngx';
import { File } from '@ionic-native/file/ngx';
import { from } from 'rxjs';
@Component({
  selector: 'app-download',
  templateUrl: './download.component.html',
  styleUrls: ['./download.component.scss']
})
export class DownloadComponent implements OnInit {
  @Input() url: string;
  //  本地存储路径
  readonly localPath = this.file.dataDirectory + 'android.apk';
  /**
   * 0 下载中
   * 1 下载完成
   * 2 下载错误
   */
  state = 0;
  /**
   * 进度
   */
  progress = 0;
  constructor(private transfer: FileTransfer, private file: File, private fileOpener: FileOpener) { }

  ngOnInit(): void {
    if (this.url) {
      this.downloadApk();
    }
  }
  downloadApk(): void {
    const fileTransfer: FileTransferObject = this.transfer.create();
    from(fileTransfer.download(this.url, this.localPath, true)).subscribe(this.onDownloadSuccess, this.onDownloaderror);
    fileTransfer.onProgress((event: ProgressEvent) => {
      this.progress = Math.floor(event.loaded / event.total);
    });
  }

  /**
   * 更新完成
   */
  onDownloadSuccess = (ret) => {
    this.state = 1;
  }
  /**
   * 失败
   */
  onDownloaderror = (error) => {
    this.state = 2;
  }

  /**
   * 安装应用
   */
  async onInstallApp(): Promise<void> {
    await this.fileOpener.open(this.localPath, 'application/vnd.android.package-archive');
  }

}
