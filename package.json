{"name": "sx-xj-app", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"start:dev:android": "ionic cordova build android --configuration=dev && cordova run android", "start:prod:android": "ionic cordova build android --configuration=production && cordova run android", "start:prod": "ionic serve  --configuration=production --open", "start:dev": "ionic serve --configuration=dev --open", "sing:android": "node scripts/build/copyjks.js &&  ionic cordova build android --configuration=production --prod  && cordova build android --release -- --keystore=platforms/android/geok.jks --storePassword=geok520 --alias=prod --password=geok520", "ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^12.2.10", "@angular/common": "~12.1.1", "@angular/core": "~12.1.1", "@angular/forms": "~12.1.1", "@angular/platform-browser": "~12.1.1", "@angular/platform-browser-dynamic": "~12.1.1", "@angular/router": "~12.1.1", "@capacitor/core": "3.2.4", "@ionic-native/android-permissions": "^5.24.0", "@ionic-native/background-geolocation": "^5.23.0", "@ionic-native/camera": "^5.36.0", "@ionic-native/core": "^5.24.0", "@ionic-native/device": "^5.36.0", "@ionic-native/file": "^5.36.0", "@ionic-native/file-opener": "^5.36.0", "@ionic-native/file-transfer": "^5.36.0", "@ionic-native/geolocation": "^5.36.0", "@ionic-native/native-storage": "^5.36.0", "@ionic-native/network": "^5.36.0", "@ionic-native/screen-orientation": "^5.36.0", "@ionic-native/sqlite": "^5.36.0", "@ionic-native/text-to-speech": "^5.36.0", "@ionic-native/vibration": "^5.36.0", "@ionic/angular": "^5.5.2", "@ionic/storage-angular": "^3.0.6", "@juggle/resize-observer": "^3.3.1", "@mauron85/cordova-plugin-background-geolocation": "^3.1.7", "@ngx-resource/core": "^10.0.0", "@ngx-resource/handler-ngx-http": "^10.0.0", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@turf/centroid": "^6.5.0", "@turf/turf": "^6.5.0", "@types/crypto-js": "^4.1.1", "@types/hammerjs": "^2.0.46", "codelyzer": "^6.0.2", "cordova-background-geolocation-plugin": "^1.1.0", "cordova-plugin-android-permissions": "^1.1.2", "cordova-plugin-file": "^6.0.2", "cordova-plugin-file-transfer": "^1.7.1", "cordova-res": "^0.15.4", "cordova-sqlite-storage": "^6.0.0", "crypto-js": "^4.2.0", "echarts": "^5.2.1", "eval5": "^1.4.7", "flv.js": "^1.6.2", "hammerjs": "^2.0.8", "jsencrypt": "^3.2.1", "ngx-echarts": "^7.0.2", "ol": "^6.9.0", "ost-utils": "^1.1.3", "rxjs": "~6.6.0", "simple-pdf-viewer": "^2.0.3", "ts-md5": "^1.2.9", "tslib": "^2.2.0", "tslint": "^6.1.3", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.1", "@angular-eslint/builder": "~12.0.0", "@angular-eslint/eslint-plugin": "~12.0.0", "@angular-eslint/eslint-plugin-template": "~12.0.0", "@angular-eslint/template-parser": "~12.0.0", "@angular/cli": "~12.1.1", "@angular/compiler": "~12.1.1", "@angular/compiler-cli": "~12.1.1", "@angular/language-service": "~12.0.1", "@capacitor/cli": "3.2.4", "@ionic/angular-toolkit": "^4.0.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "4.16.1", "@typescript-eslint/parser": "4.16.1", "android": "0.0.8", "cordova-android": "^9.1.0", "cordova-plugin-background-geolocation": "^1.0.6", "cordova-plugin-camera": "^5.0.3", "cordova-plugin-device": "^2.0.2", "cordova-plugin-dialogs": "^2.0.2", "cordova-plugin-file-opener2": "^3.0.5", "cordova-plugin-geolocation": "^4.1.0", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "^4.2.1", "cordova-plugin-network-information": "^3.0.0", "cordova-plugin-screen-orientation": "^3.0.4", "cordova-plugin-splashscreen": "^5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-tts": "^0.2.3", "cordova-plugin-vibration": "^3.1.1", "cordova-plugin-whitelist": "^1.3.3", "cordova.plugin.installapk": "^1.0.2", "es6-promise-plugin": "^4.2.2", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~4.2.4"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-ionic-webview": {"ANDROID_SUPPORT_ANNOTATIONS_VERSION": "27.+"}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-android-permissions": {}, "cordova-plugin-background-geolocation": {"GOOGLE_PLAY_SERVICES_VERSION": "11+", "ANDROID_SUPPORT_LIBRARY_VERSION": "26+", "ICON": "@mipmap/icon", "SMALL_ICON": "@mipmap/icon", "ACCOUNT_NAME": "@string/app_name", "ACCOUNT_LABEL": "@string/app_name", "ACCOUNT_TYPE": "$PACKAGE_NAME.account", "CONTENT_AUTHORITY": "$PACKAGE_NAME"}, "cordova-plugin-file": {}, "cordova-plugin-file-transfer": {}, "cordova-plugin-file-opener2": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova.plugin.installapk": {}, "cordova-plugin-geolocation": {}, "cordova-sqlite-storage": {}, "cordova-plugin-camera": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova-plugin-screen-orientation": {}, "cordova-plugin-vibration": {}, "cordova-plugin-tts": {}}, "platforms": ["android"]}}