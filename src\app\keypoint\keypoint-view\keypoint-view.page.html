<ion-header [translucent]="true" class="compact-header">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button (click)="goBack()" defaultHref="/tabs/home"></ion-back-button>
    </ion-buttons>
    <ion-title size="small">关键点管理</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [scrollY]="selectedTab === 'map'">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">关键点查看</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Tab导航 -->
  <ion-segment 
    [(ngModel)]="selectedTab" 
    (ionChange)="onTabChange($event)"
    class="tab-segment">
    <ion-segment-button value="map">
      <ion-icon name="map-outline"></ion-icon>
      <ion-label>地图查看</ion-label>
    </ion-segment-button>
    <ion-segment-button value="list">
      <ion-icon name="list-outline"></ion-icon>
      <ion-label>列表管理</ion-label>
    </ion-segment-button>
  </ion-segment>

  <!-- 地图视图 -->
  <div class="view-container" *ngIf="selectedTab === 'map'">
    <!-- 悬浮筛选控制面板 -->
    <div class="map-filter-panel">
      <app-map-filter-panel 
        [filter]="filter"
        (filterChange)="onFilterChange($event)"
        (layerExtentRequest)="onLayerExtentRequest($event)">
      </app-map-filter-panel>
    </div>


    
    <!-- 地图容器 -->
    <div class="map-container">
      <ost-map
        #mapComponent
        [layerIds]="layerIds"
        [showLocationProviderButton]="false"
       >
      </ost-map>
      
      <!-- 图例面板 -->
       <div class="legend-panel-fixed">
         <div class="legend-header">
           <span class="legend-title">图例</span>
         </div>
         <div class="legend-content">
           <div class="legend-item" *ngFor="let item of legendItems">
            <div class="legend-icon" [style.border-color]="item.color">
              <div class="inner-circle"></div>
            </div>
            <span class="legend-label">{{ item.label }}</span>
          </div>
         </div>
       </div>
    </div>
  </div>

  <!-- 列表视图 -->
  <div class="view-container" *ngIf="selectedTab === 'list'">
    <app-keypoint-list-view
      [filter]="filter"
      (filterChange)="onFilterChange($event)">
    </app-keypoint-list-view>
  </div>

  <!-- 加载状态 -->
  <div class="loading-container" *ngIf="loading">
    <ion-spinner name="crescent"></ion-spinner>
    <div class="loading-text">加载中...</div>
  </div>
</ion-content>