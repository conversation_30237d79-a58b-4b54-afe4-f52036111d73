<!-- 容器 -->
<div class="stake-menu-container">
  <!-- 紧凑搜索区域 -->
  <div class="compact-search-container" *ngIf="showSearch">
    <!-- 第一行：桩类型选择 -->
    <div class="search-row">
      <div class="search-item stake-type-item">
        <span class="search-label">桩类型：</span>
        <ost-quick-option-select
          #stakeTypeSelector
          class="stake-type-selector"
          [(ngModel)]="stakeType"
          (ngModelChange)="onStakeTypeChange($event)"
          [labelValue]="'value'"
          [labelName]="'name'"
          placeholder="请选择桩类型"
          [disabled]="loading"
          icon="layers-outline">
        </ost-quick-option-select>
      </div>
      <ion-icon name="search-outline" class="search-trigger-icon" (click)="onSearch()"></ion-icon>
    </div>

    <!-- 第二行：关键词搜索和重置 -->
    <div class="search-row">
      <div class="search-item search-input-item">
        <span class="search-label">关键词：</span>
        <ion-input
          class="compact-search-input"
          [placeholder]="searchPlaceholder"
          [(ngModel)]="params"
        ></ion-input>
      </div>
      <ion-note class="reset-link" (click)="onReset()">重置</ion-note>
    </div>
  </div>

  <!-- 内容区域 - 使用 ion-content 支持无限滚动 -->
  <ion-content class="content-scrollable" [style.height.px]="contentHeight" [scrollEvents]="true">
    <!-- 优化的加载状态 -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-content">
        <ion-spinner name="bubbles" color="primary"></ion-spinner>
        <span class="loading-text">{{ loadingText }}</span>
      </div>
    </div>

    <div *ngIf="items.length<=0 && !loading;" class="no-data">
      <img src="assets/menu/box2.png" style="padding-top: 50px;" />
      <!-- 暂无数据 -->
      <span class="no-data-span">暂无数据</span>
    </div>

    <ost-tree-list
      *ngIf="items.length > 0"
      #menu
      [items]="items"
      (toggleSubMenu)="onToggleSubMenu($event)"
      (itemClick)="onItemClick($event)">
    </ost-tree-list>

    <!-- 加载更多 -->
    <ion-infinite-scroll *ngIf="isPage" threshold="50px" (ionInfinite)="loadMoreData($event)">
      <ion-infinite-scroll-content
        [loadingSpinner]="spinner"
        [loadingText]="isShowNoMoreStr">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-content>
</div>