import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ShareModuleService } from '../../share.service';
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';

export interface KeyPoint {
  pointCode: string;
  pointName: string;
  stakeName: string;
  depName: string;
  isItRaining: string;
  point: string;
  depCode: string;
  stakeCode: string;
  taskPointCode: string;
  isDeleted: string;
  taskCode: string;
  bufferRange: number;
  createTime: string;
  id: string;
  state: string;
  bufferTrans: number;
}

@Injectable({
  providedIn: 'root'
})
export class KeyPointService {
  // 获取关键点接口地址
  private readonly apiUrl = '/work-inspect/api/v2/inspect/app/task/point/status';
  // 打卡接口地址
  private readonly punchInApiUrl = '/work-inspect/api/v2/inspect/app/trajectory/clock';

  constructor(
    private netSer: ShareModuleService,
    private UserInfoSer: UserInfoService,
    private dataSyncManager: DataSyncManagerService,
  ) { }

  /**
   * 根据任务编码获取所有关键点
   * @param taskCode 任务编码
   */
  getKeyPointsByTaskCode(taskCode: string): Observable<any> {
    return this.netSer.getRequest({
      interfaceUrl: this.apiUrl,
      taskCode
    });
  }

  /**
   * 自动打卡并支持本地缓存+断点续传+批量上传
   * 到达关键点时，自动将本次打卡数据加入本地缓存，网络恢复后自动批量上传
   * @param taskCode 任务编码
   * @param pointCode 关键点编码
   * @param longitude 当前经度
   * @param latitude 当前纬度
   * @returns 返回提交结果，包含是否需要刷新关键点数据的标识
   */
  async clockInWithCacheBatch(taskCode: string, pointCode: string, longitude: number, latitude: number): Promise<{ shouldRefreshKeyPoints: boolean }> {
    const keyPointData = {
      taskCode,
      pointCode,
      longitude,
      latitude,
      userCode: this.UserInfoSer.userId,
      userName: this.UserInfoSer.userName,
      depCode: this.UserInfoSer.depCode,
      trajectoryTime: Date.now()
    };

    const result = await this.dataSyncManager.addToCache(
      SyncDataType.AUTO_KEY_POINT_CLOCK_IN,
      keyPointData,
      `${ResourceGlobalConfig.url}${this.punchInApiUrl}`
    );

    // 根据提交结果返回是否需要刷新关键点数据
    return {
      shouldRefreshKeyPoints: result.status === 'uploaded' && result.code === 0
    };
  }
} 