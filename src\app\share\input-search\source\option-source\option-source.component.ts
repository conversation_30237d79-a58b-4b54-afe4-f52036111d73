import { ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { Platform } from '@ionic/angular';
import { InputSearchSourceService } from '../../input-search-source.service';
import { ShareModuleService } from 'src/app/share/share.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'ost-option-source',
  templateUrl: './option-source.component.html',
  styleUrls: ['./option-source.component.scss']
})
export class OptionSourceComponent implements OnInit, OnChanges {
  @Input() radioValue: string;
  // 选项数据
  @Input() radioList = [];
  @Input() labelName: string;
  @Input() labelValue: string;
  // 接口
  @Input() interfaceUrl: string;
  // 搜索参数
  params = '';
  list: any;
  // 标记是否已经从外部接收到数据
  private hasReceivedData = false;
  
  constructor(
    public searchSer: InputSearchSourceService, public cd: ChangeDetectorRef,
    public platform: Platform, public shareSer: ShareModuleService
  ) { }
  
  ngOnChanges(changes: SimpleChanges): void {
    // 检查radioList是否有有效数据传入
    if (changes.radioList && changes.radioList.currentValue) {
      const radioList = changes.radioList.currentValue;
      if (Array.isArray(radioList) && radioList.length > 0) {
        this.hasReceivedData = true;
        this.initData(radioList);
      }
    }
  }

  ngOnInit() {
    // 确保只有在没有通过Input接收到数据且interfaceUrl存在时才发起请求
    setTimeout(() => {
      if (!this.hasReceivedData && this.interfaceUrl && (!this.radioList || this.radioList.length === 0)) {
        this.loadOptions();
      }
    });
  }

  loadOptions(): void {
    const url = `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}${this.interfaceUrl}`;
    this.getRequest(url);
  }

  getRequest(url: string) {
    this.shareSer.selectOptionRequest({ interfaceUrl: url }).subscribe(res => {
      this.radioList = res.data;
      this.initData(res.data);
    });
  }

  /**
   * 重置
   */
  onReset(): void {
    this.params = '';
    this.initData(this.radioList);
  }

  /**
   * 搜索
   */
  onSearch() {
    this.list = this.filterDataByKeyword(this.list, this.params);
  }

  /**
   * 过滤数据
   * @param data 
   * @param keyword 
   */
  filterDataByKeyword(data: any, keyword: string) {
    const filteredData: any = data.filter(item => item.name.includes(keyword));
    return filteredData;
  }

  /**
   * 初始化数据
   * @param data 
   */
  initData(data: any) {
    if (this.labelName && this.labelValue) {
      this.transformation(data, this.labelValue, this.labelName);
    } else {
      this.list = data;
    }
    // 值回显
    if (this.radioValue) {
      this.onChangeRadio(this.radioValue);
    }
  }

  /**
   * 处理单选项点击事件
   * @param item 选中的选项
   */
  onRadioClick(item: any) {
    this.onChangeRadio(item.value);
  }

  /**
   * 值改变
   * @param value 
   */
  onChangeRadio(value: any) {
    const RadioName = this.getRadioName(value);
    this.radioValue = value;
    this.searchSer.change(RadioName, value);
  }

  /**
   * 获取选项名称
   * @param value 
   */
  getRadioName(value: any) {
    if (this.list.length > 0) {
      return this.list.find(item => item.value === value)?.name;
    }
  }

  /**
   * 转换数据
   * @param result 
   * @param codeKey 
   * @param nameKey 
   */
  transformation(result: any, codeKey: string, nameKey: string) {
    const newList = [];
    result.forEach(i => {
      const item = {
        value: i[codeKey],
        name: i[nameKey]
      };
      newList.push(item);
    });
    this.list = newList;
    this.cd.detectChanges();
  }
}
