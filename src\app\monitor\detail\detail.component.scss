@import "../../../theme/variables.scss";
.monitor-top {
    height: 50%;
    position: relative;
}
.monitor-center {
    .title {
        background: var(--ion-color-secondary);
        padding: 10px 16px;
        color: var(--ion-color-secondary-contrast);
        font-size: 14px;
    }
    .tips {
        background: #cee1ff;
        font-size: 12px;
        padding: 5px 16px;
        .on-line {
            color: var(--ion-color-success);
        }
        .off-line {
            color: var(--ion-color-danger);
            position: absolute;
            right: 16px;
        }
    }
}
.monitor-bottom {
    .select{
        background-color: var(--ion-color-secondary);
        color: #fff;
    }
    .location-success{
        color: var(--ion-color-success);
    }
    .location-primary{
        color: var(--ion-color-primary);
    }
    .user {
        border: 1px solid #eee;
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        .user-name {
            position: absolute;
            left: 16px;
        }
        .on-location{
            position: absolute;
            right: 16px;
            ion-icon{
                font-size: 10px;
            }
        }
    }
}

// 深色主题 将背景设置为白色
.ios{--ion-background-color:white}
// 安卓
.md{--ion-background-color:white}