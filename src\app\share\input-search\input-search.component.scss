// 搜索框禁用样式
.input-search-readonly {
  touch-action: none;
  pointer-events: none;
}

.input-search {
  display: flex;
  align-items: center;
  width: 100%;

  ion-input {
    flex: 1;
    min-width: 0;
  }

  ion-icon {
    flex-shrink: 0;
    font-size: 19px;
    padding: 4px;
  }

  .search-text {
    flex-shrink: 0;
    font-size: 13px;
    color: #007bff;
    padding: 4px 8px;
    border: 1px solid #007bff;
    border-radius: 4px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;

    &:hover {
      background-color: #007bff;
      color: #ffffff;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  ion-button {
    flex-shrink: 0;
  }
}