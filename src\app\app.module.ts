import { DatePipe } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouteReuseStrategy } from '@angular/router';
import { AndroidPermissions } from '@ionic-native/android-permissions/ngx';
import { BackgroundGeolocation } from '@ionic-native/background-geolocation/ngx';
import { Device } from '@ionic-native/device/ngx';
import { FileOpener } from '@ionic-native/file-opener/ngx';
import { FileTransfer } from '@ionic-native/file-transfer/ngx';
import { File } from '@ionic-native/file/ngx';
import { Geolocation } from '@ionic-native/geolocation/ngx';
import { Network } from '@ionic-native/network/ngx';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { IonicStorageModule } from '@ionic/storage-angular';
import { ResourceModule } from '@ngx-resource/handler-ngx-http';
import * as echarts from 'echarts';
import { NgxEchartsModule } from 'ngx-echarts';
import { StorageService } from './@core/providers/storage.service';
import { AppRoutingModule } from './app-routing.module';
import { AppBootService } from './app.boot';
import { AppComponent } from './app.component';
import { ExecutModule } from './execut/execut.module';
import { ShareModule } from './share/share.module';
import { VersionModule } from './share/version/version.module';
import { Camera } from '@ionic-native/camera/ngx';
import { ScreenOrientation } from '@ionic-native/screen-orientation/ngx';
import { Vibration } from '@ionic-native/vibration/ngx';
import { TextToSpeech } from '@ionic-native/text-to-speech/ngx';
import { CachingInterceptor } from './@core/net/interceptors/caching.interceptor';
import { EncryptionInterceptor } from './@core/net/interceptors/encryption.interceptor';
import { ResponseHandlerInterceptor } from './@core/net/interceptors/response-handler.interceptor';
import { TimestampInterceptor } from './@core/net/interceptors/timestamp.interceptor';
import { UrlRewriteInterceptor } from './@core/net/interceptors/url-rewrite.interceptor';

@NgModule({
  declarations: [AppComponent],
  entryComponents: [],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    HttpClientModule,
    VersionModule,
    ResourceModule.forRoot(),
    IonicStorageModule.forRoot(),
    IonicModule.forRoot(),
    ShareModule,
    ExecutModule,
    NgxEchartsModule.forRoot({ echarts }),
  ],
  providers: [
    AppBootService, Network,Device,
    // 1. 缓存层：最先接触请求，可直接返回缓存，避免后续所有操作
    { provide: HTTP_INTERCEPTORS, useClass: CachingInterceptor, multi: true },
    // 2. 时间戳层：为所有通过缓存层的请求添加时间戳
    { provide: HTTP_INTERCEPTORS, useClass: TimestampInterceptor, multi: true },
    // 3. URL重写层：构建最终请求的URL
    { provide: HTTP_INTERCEPTORS, useClass: UrlRewriteInterceptor, multi: true },
    // 4. 加密层：对构建好的请求进行加密，并对返回的响应解密
    { provide: HTTP_INTERCEPTORS, useClass: EncryptionInterceptor, multi: true },
    // 5. 响应处理层：最后接触请求，最先接触响应，处理业务码和全局错误
    { provide: HTTP_INTERCEPTORS, useClass: ResponseHandlerInterceptor, multi: true },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    Geolocation,
    AndroidPermissions,
    BackgroundGeolocation,
    StorageService,
    FileTransfer,
    File,Camera,
    FileOpener,
    DatePipe, // 时间管道工具
    SQLite,
    ScreenOrientation,
    Vibration,
    TextToSpeech
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
