import { Component, Input, OnInit } from '@angular/core';
import { PopoverController } from '@ionic/angular';
import { Collection } from 'ol';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
@Component({
  selector: 'ost-map-switch',
  templateUrl: './ost-map-switch.component.html',
  styleUrls: ['./ost-map-switch.component.scss']
})
export class MapSwitchComponent implements OnInit {
  // 创建地图
  @Input() baseLayer: LayerGroup;
  // 基础图层集合
  @Input() baseLayerList: Collection<LayerGroup> = new Collection();
  // 创建业务图层
  @Input() businessLayer!: LayerGroup;
  // 业务图层集合
  @Input() businessLayerList: Collection<BaseLayer> = new Collection();

  constructor(public popoverController: PopoverController) { }

  ngOnInit(): void {
    // 初始化透明度
    this.businessLayerList.forEach(lyaer => {
      if (!lyaer.get('opacity')) {
        lyaer.set('opacity', 1);
      }
    });
  }


  /**
   * 图层切换功能
   */
  onMapServe(item: LayerGroup): void {
    this.baseLayerList.forEach(lyaer => { lyaer.set('selected', false); });
    item.set('selected', true);
    this.baseLayer.setLayers(item.getLayers());
    this.popoverController.dismiss();
  }

  /**
   * 业务图层选择功能
   * @param value 复选框值 true/false
   * @param item 当前操作图层
   */
  onChangeBusiness(value: boolean, item: BaseLayer): void {
    const newLayer = new Collection<BaseLayer>();
    this.businessLayerList.getArray().map((layer) => {
      if (item.get('id') === layer.get('id')) {
        layer.set('selected', value);
      }
      if (layer.get('selected')) {
        newLayer.push(layer);
      }
    });
    this.businessLayer.setLayers(newLayer);
  }
  /**
   * 管线透明度
   */
  opacityChange($event: number, item: BaseLayer): void {

    this.businessLayerList.getArray().map((layer) => {
      if (item.get('id') === layer.get('id')) {
        layer.set('opacity', $event / 100);
      }
    });
    this.businessLayer.setLayers(this.businessLayerList);
  }
  /**
   * 关闭窗口
   */
  onClose(): void {
    this.popoverController.dismiss();
  }

}
