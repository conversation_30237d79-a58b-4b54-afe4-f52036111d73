import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Ion<PERSON>ontent, AlertController, ToastController, ModalController } from '@ionic/angular';
import { KeyPoint, FilterOptions } from '../../keypoint-view.page';
import { KeypointViewService, KeyPointQueryParams, UpdateKeyPointParams } from '../../keypoint-view.service';
import { KeypointEditModalComponent } from '../keypoint-edit-modal/keypoint-edit-modal.component';

@Component({
  selector: 'app-keypoint-list-view',
  templateUrl: './keypoint-list-view.component.html',
  styleUrls: ['./keypoint-list-view.component.scss']
})
export class KeypointListViewComponent implements OnInit, OnDestroy {
  @Input() filter: FilterOptions;
  @Output() filterChange = new EventEmitter<Partial<FilterOptions>>();
  @ViewChild(IonContent, { static: false }) content: IonContent;

  keyPoints: KeyPoint[] = [];
  loading = false;
  searchText = '';
  filteredKeyPoints: KeyPoint[] = [];

  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  hasMoreData = true;
  isLoadingMore = false;
  spinnerType = 'crescent';

  // 选择模式相关属性
  isSelectionMode = false;
  selectedItems: Set<string> = new Set();

  get currentLoadingText(): string {
    return this.hasMoreData ? '加载更多...' : '没有更多数据了';
  }

  private destroy$ = new Subject<void>();

  constructor(
    private keypointViewService: KeypointViewService,
    private alertController: AlertController,
    private toastController: ToastController,
    private modalController: ModalController
  ) { }

  ngOnInit() {
    this.loadKeyPoints(true);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 搜索框输入事件
   */
  onSearchChange(event: any) {
    this.searchText = event.detail?.value?.toLowerCase() || '';
    this.scrollToTop();
    this.applySearch();
  }

  /**
   * 应用搜索筛选
   */
  private applySearch() {
    if (!this.searchText) {
      this.filteredKeyPoints = [...this.keyPoints];
    } else {
      this.filteredKeyPoints = this.keyPoints.filter(point =>
        point.pointName.toLowerCase().includes(this.searchText) ||
        point.name.toLowerCase().includes(this.searchText)
      );
    }
  }

  /**
   * 巡检类型筛选
   */
  onInspectionTypeChange(type: 'all' | 'vehicle' | 'person') {
    this.filterChange.emit({ inspectionType: type });
    this.scrollToTop();
    setTimeout(() => {
      this.loadKeyPoints(true);
    }, 100);
  }

  /**
   * 滚动到列表顶部
   */
  private scrollToTop() {
    if (this.content) {
      this.content.scrollToTop(300);
    }
  }

  onKeyPointClick(keyPoint: KeyPoint) {
    // TODO: 添加查看详情的逻辑
  }

  /**
   * 获取类型文本
   */
  getTypeText(keyPoint: KeyPoint): string {
    switch (keyPoint.type) {
      case 'vehicle':
        return '巡视';
      case 'person':
        return '巡查';
      default:
        return '未知';
    }
  }



  /**
   * 检查筛选按钮是否激活
   */
  isInspectionTypeActive(type: 'all' | 'vehicle' | 'person'): boolean {
    return this.filter?.inspectionType === type;
  }



  /**
   * 转换API数据为KeyPoint格式
   */
  private convertApiDataToKeyPoints(apiData: any[]): KeyPoint[] {
    return apiData?.filter(Boolean).map(item => {
      let coordinate: [number, number];
      try {
        const geomData = typeof item.geom === 'string' ? JSON.parse(item.geom) : item.geom;
        if (geomData && geomData.coordinates && Array.isArray(geomData.coordinates)) {
          coordinate = [geomData.coordinates[0], geomData.coordinates[1]];
        } else {
          coordinate = [0, 0];
        }
      } catch {
        coordinate = [0, 0];
      }

      return {
        id: item.pointCode,
        pointCode: item.pointCode,
        pointName: item.pointName,
        name: item.pointName,
        type: item.inspectionMethod === '巡视' ? 'vehicle' as const : 'person' as const,
        coordinate,
        isRainyDay: item.isItRaining === '是',
        isItRaining: item.isItRaining,
        bufferRange: item.bufferRange,
        geom: item.geom,
        state: 'pending',
        status: 'pending' as const,
        point: item.geom
      };
    }) || [];
  }

  /**
   * 构建查询参数
   */
  private buildQueryParams(): KeyPointQueryParams {
    return {
      pageNum: this.currentPage,
      pageSize: this.pageSize,
      inspectionMethod: this.filter?.inspectionType === 'vehicle' ? '巡视' :
        this.filter?.inspectionType === 'person' ? '巡查' : undefined,
      isItRaining: this.filter?.showRainyDay ? '是' : undefined
    };
  }

  /**
   * 加载关键点数据
   * @param isRefresh 是否为刷新操作
   */
  private loadKeyPoints(isRefresh: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
      if (isRefresh) {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.loading = true;
      } else {
        this.isLoadingMore = true;
      }

      const params = this.buildQueryParams();

      this.keypointViewService.getKeyPointsGrid(params)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (result) => {
            if (result.code === 0 && result.data) {
              const convertedKeyPoints = this.convertApiDataToKeyPoints(result.data.records);

              this.totalCount = result.data.total || 0;

              if (isRefresh) {
                this.keyPoints = convertedKeyPoints;
              } else {
                this.keyPoints = [...this.keyPoints, ...convertedKeyPoints];
              }

              this.hasMoreData = this.keyPoints.length < this.totalCount;
              this.applySearch();
              resolve();
            } else {
              reject(new Error(result.msg || '加载失败'));
            }
          },
          error: (error) => {
            if (!isRefresh && this.currentPage > 1) {
              this.currentPage--;
            }
            reject(error);
          },
          complete: () => {
            this.loading = false;
            this.isLoadingMore = false;
          }
        });
    });
  }

  /**
   * 上拉加载更多
   */
  loadMore(event): void {
    this.isLoadingMore = true;

    if (this.totalCount > this.currentPage * this.pageSize) {
      this.currentPage++;

      const params = this.buildQueryParams();

      this.keypointViewService.getKeyPointsGrid(params)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (result) => {
            if (result.code === 0 && result.data) {
              const convertedKeyPoints = this.convertApiDataToKeyPoints(result.data.records);

              this.keyPoints = [...this.keyPoints, ...convertedKeyPoints];
              this.applySearch();

              this.totalCount = result.data.total || this.totalCount;
              this.hasMoreData = this.keyPoints.length < this.totalCount;
            } else {
              this.currentPage--;
            }
            this.isLoadingMore = false;
            event.target.complete();
          },
          error: (error) => {
            this.currentPage--;
            this.isLoadingMore = false;
            event.target.complete();
          }
        });
    } else {
      this.hasMoreData = false;
      this.spinnerType = null;
      this.isLoadingMore = false;
      setTimeout(() => {
        event.target.complete();
      }, 1000);
    }
  }

  /**
   * 切换选择模式
   */
  toggleSelectionMode(): void {
    this.isSelectionMode = !this.isSelectionMode;
    if (!this.isSelectionMode) {
      this.selectedItems.clear();
    }
  }

  /**
   * 选择/取消选择项目
   */
  onItemSelect(pointCode: string, event: any): void {
    if (event.detail.checked) {
      this.selectedItems.add(pointCode);
    } else {
      this.selectedItems.delete(pointCode);
    }
  }

  /**
   * 编辑关键点
   */
  async editKeyPoint(keyPoint: KeyPoint): Promise<void> {
    try {
      const modal = await this.modalController.create({
        component: KeypointEditModalComponent,
        componentProps: {
          keyPoint: { ...keyPoint }
        },
        cssClass: 'keypoint-edit-modal'
      });

      await modal.present();

      const { data, role } = await modal.onWillDismiss();

      if (role === 'save' && data) {
        await this.updateKeyPoint(data);
      }
    } catch (error) {
      console.error('打开编辑模态框失败:', error);
      this.showToast('打开编辑界面失败，请重试');
    }
  }

  /**
   * 删除单个关键点
   */
  async deleteKeyPoint(keyPoint: KeyPoint): Promise<void> {
    const alert = await this.alertController.create({
      header: '确认删除',
      message: `确定要删除关键点 "${keyPoint.pointName}" 吗？`,
      buttons: [
        {
          text: '取消',
          role: 'cancel'
        },
        {
          text: '删除',
          role: 'destructive',
          handler: () => {
            this.performDelete([keyPoint.pointCode]);
          }
        }
      ]
    });
    await alert.present();
  }

  /**
   * 删除选中的关键点
   */
  async deleteSelected(): Promise<void> {
    if (this.selectedItems.size === 0) {
      this.showToast('请先选择要删除的关键点');
      return;
    }

    const alert = await this.alertController.create({
      header: '确认删除',
      message: `确定要删除选中的 ${this.selectedItems.size} 个关键点吗？`,
      buttons: [
        {
          text: '取消',
          role: 'cancel'
        },
        {
          text: '删除',
          role: 'destructive',
          handler: () => {
            this.performDelete(Array.from(this.selectedItems));
          }
        }
      ]
    });
    await alert.present();
  }

  /**
   * 执行删除操作
   */
  private async performDelete(pointCodes: string[]): Promise<void> {
    try {
      console.log('删除关键点:', pointCodes);
      // 使用数组转换方式确保参数格式正确
      const result = await this.keypointViewService.deleteKeyPoints( pointCodes).toPromise();

      if (result.code === 0) {
        // 从本地数据中移除已删除的项目
        this.keyPoints = this.keyPoints.filter(item => !pointCodes.includes(item.pointCode));
        this.applySearch();

        // 清空选择
        this.selectedItems.clear();
        this.isSelectionMode = false;

        this.showToast('删除成功');
      } else {
        this.showToast(result.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除关键点失败:', error);
      this.showToast('删除失败，请重试');
    }
  }

  /**
   * 更新关键点
   */
  async updateKeyPoint(keyPoint: KeyPoint): Promise<void> {
    try {
      const params: UpdateKeyPointParams = {
        pointCode: keyPoint.pointCode,
        pointName: keyPoint.pointName,
        pointGeom: keyPoint.geom,
        bufferRange: keyPoint.bufferRange,
        isItRaining: keyPoint.isItRaining,
        inspectionMethod: keyPoint.type === 'vehicle' ? '巡视' : '巡查'
      };

      const result = await this.keypointViewService.updateKeyPoint(params).toPromise();

      if (result.code === 0) {
        // 更新本地数据
        const index = this.keyPoints.findIndex(item => item.pointCode === keyPoint.pointCode);
        if (index !== -1) {
          this.keyPoints[index] = { ...keyPoint };
          this.applySearch();
        }

        this.showToast('更新成功');
      } else {
        this.showToast(result.msg || '更新失败');
      }
    } catch (error) {
      console.error('更新关键点失败:', error);
      this.showToast('更新失败，请重试');
    }
  }

  /**
   * 显示提示信息
   */
  private async showToast(message: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      position: 'bottom'
    });
    await toast.present();
  }
}